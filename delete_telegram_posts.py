#!/usr/bin/env python3
"""
Script to delete specific Telegram posts using bot token
"""

import requests
import sys
import json

# Bot configuration
BOT_TOKEN = "7592975085:AAE63KwijvlIdsQpCoB62MKpdGftZwgLvq0"
CHAT_ID = "-1002269711567"

# Message IDs to delete (extracted from the URLs)
MESSAGE_IDS = [1939, 1937]

def delete_message(bot_token, chat_id, message_id):
    """
    Delete a specific message using Telegram Bot API
    
    Args:
        bot_token (str): The bot token
        chat_id (str): The chat ID
        message_id (int): The message ID to delete
    
    Returns:
        dict: API response
    """
    url = f"https://api.telegram.org/bot{bot_token}/deleteMessage"
    
    payload = {
        "chat_id": chat_id,
        "message_id": message_id
    }
    
    try:
        response = requests.post(url, json=payload)
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"ok": False, "error": str(e)}

def main():
    """Main function to delete the specified messages"""
    print("Starting Telegram post deletion...")
    print(f"Chat ID: {CHAT_ID}")
    print(f"Messages to delete: {MESSAGE_IDS}")
    print("-" * 50)
    
    success_count = 0
    failed_count = 0
    
    for message_id in MESSAGE_IDS:
        print(f"Deleting message {message_id}...")
        
        result = delete_message(BOT_TOKEN, CHAT_ID, message_id)
        
        if result.get("ok", False):
            print(f"✅ Successfully deleted message {message_id}")
            success_count += 1
        else:
            print(f"❌ Failed to delete message {message_id}")
            print(f"   Error: {result.get('description', 'Unknown error')}")
            failed_count += 1
        
        print()
    
    print("-" * 50)
    print(f"Summary:")
    print(f"Successfully deleted: {success_count}")
    print(f"Failed to delete: {failed_count}")
    
    if failed_count > 0:
        print("\nPossible reasons for failure:")
        print("- Bot doesn't have delete permissions in the chat")
        print("- Message doesn't exist or was already deleted")
        print("- Bot token is invalid or expired")
        print("- Chat ID is incorrect")
        sys.exit(1)
    else:
        print("\n🎉 All messages deleted successfully!")

if __name__ == "__main__":
    main()
